# -*- coding: utf-8 -*-
from odoo import api, fields, models, _


class ApprovalCategory(models.Model):
    _inherit = 'approval.category'

    th_template = fields.Binary("Template")
    th_template_name = fields.Char("File name")
    th_sector_id = fields.Many2one('th.approval.sector', string='Lĩnh vực')

    def create_new_request(self):
        self.ensure_one()
        # If category uses sequence, set next sequence as name
        # (if not, set category name as default name).
        return {
            "type": "ir.actions.act_window",
            "res_model": "approval.request",
            "views": [[False, "form"]],
            "context": {
                'form_view_initial_mode': 'edit',
                'default_name': _('New') if self.automated_sequence else self.name,
                'default_th_sector_id': self.th_sector_id.id,
                'default_category_id': self.id,
                'default_request_owner_id': self.env.user.id,
                'default_request_status': 'new'
            },
        }

    def write(self, vals):
        res = super(ApprovalCategory, self).write(vals)
        if 'approver_ids' in vals:
            for category in self:
                # L<PERSON>y danh sách user_id từ approver_ids
                new_user_ids = category.approver_ids.mapped('user_id').ids
                # Tìm tất cả các yêu cầu phê duyệt có category_id tương ứng
                requests = self.env['approval.request'].search([('category_id', '=', category.id)])
                for req in requests:
                    old_status = req.request_status
                    req.approver_ids = [
                        (5, 0, 0),  # Xoá toàn bộ dòng cũ
                        *[
                            (0, 0, {'user_id': user_id})
                            for user_id in new_user_ids
                        ]
                    ]
                    if req.request_status != old_status:
                        req.write({'request_status': old_status})
        return res