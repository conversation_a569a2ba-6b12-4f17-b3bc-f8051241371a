<?xml version="1.0"?>
<odoo>
    <data>

        <record id="th_approvals_external_user" model="res.groups">
            <field name="name"><PERSON><PERSON><PERSON><PERSON> dùng phê duyệt bên ng<PERSON><PERSON><PERSON> h<PERSON> thống</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="th_approvals.th_group_approval_manager_user" model="res.groups">
            <field name="name">Quản lý: Xem được tất cả các phiếu của nhân viên</field>
            <field name="comment">Quản lý có thể xem tất cả các phiếu của nhân viên trong phòng ban.</field>
            <field name="category_id" ref="base.module_category_human_resources_approvals"/>
            <field name="implied_ids" eval="[(6, 0, [ref('base.group_user')])]"/>
        </record>

        <record id="approvals.group_approval_user" model="res.groups">
            <field name="name">Officer : Approve all requests</field>
            <field name="category_id" ref="base.module_category_human_resources_approvals"/>
            <field name="implied_ids" eval="[(6, 0, [ref('th_approvals.th_group_approval_manager_user')])]"/>
            <field name="comment">The user will be able to access all requests and approve/refuse them.</field>
        </record>

        <record id="approvals.group_approval_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="comment">The user will have access to the approvals configuration.</field>
            <field name="category_id" ref="base.module_category_human_resources_approvals"/>
            <field name="implied_ids" eval="[(4, ref('approvals.group_approval_user'))]"/>
        </record>

        <record id="th_approval_manager_user_rule" model="ir.rule">
            <field name="name">Department Manager Approval Rule</field>
            <field name="model_id" ref="approvals.model_approval_request"/>
            <field name="groups" eval="[(4, ref('th_approvals.th_group_approval_manager_user'))]"/>
            <field name="domain_force">['|', '|', '|',
                ('request_owner_id.employee_id.parent_id.user_id', '=', user.id),
                ('request_owner_id.employee_id.parent_id.parent_id.user_id', '=', user.id),
                ('request_owner_id.employee_id.parent_id.parent_id.parent_id.user_id', '=', user.id),
                ('request_owner_id.employee_id.parent_id.parent_id.parent_id.parent_id.user_id', '=', user.id)]
            </field>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>
    </data>
</odoo>