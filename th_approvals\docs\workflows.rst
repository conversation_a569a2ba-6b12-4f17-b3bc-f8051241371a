II. <PERSON> tiết chức năng

A. <PERSON><PERSON><PERSON>n lý yêu cầu phê duyệt của tôi
    1. <PERSON>ồ<PERSON> tạo yêu cầu mới
       - Người dùng truy cập vào menu "Phê duyệt của tôi" chọ<PERSON> "Yêu cầu mới chọn "Danh mục phê duyệt" và tạo yêu cầu phê duyệt mới.
       - Nhập thông tin:
         • Tiêu đề phê duyệt
         • Lĩ<PERSON> vực phê duyệt
         • <PERSON><PERSON> mục phê duyệt
         • Sản phẩm (<PERSON><PERSON> hiển thị tab nhập sản phẩm cần tích chọn sản phẩm trong cấu hình loại phê duyệt)
       - Hệ thống tự động:
         • G<PERSON> những người phê duyệt theo cấu hình loại phê duyệt
         • Tạ<PERSON> yêu cầu ở trạng thá<PERSON> "<PERSON>ớ<PERSON>"

    2. <PERSON><PERSON><PERSON> phê duyệt
        2.1. <PERSON><PERSON> trình xử lý:
            - Người phê duyệt được thông báo về yêu cầu mới
            - Tại form yêu cầu có thể:
                • Phê duyệt hoặc từ chối
                • Đánh dấu hoàn thành sau khi phê duyệt
            - Trạng thái yêu cầu được cập nhật tự động
        2.2. Theo dõi tiến trình:
            - Người tạo yêu cầu có thể theo dõi:
                • Trạng thái yêu cầu
                • Lịch sử những người phê duyệt
                • Các ghi chú/bình luận


    3. Các hàm liên quan
        3.1. approval_request:
            - create(): Tạo mới yêu cầu phê duyệt
            - write(): Cập nhật thông tin yêu cầu
            - action_confirm(): Xác nhận và gửi yêu cầu phê duyệt
            - action_download_template(): Tải template mẫu của danh mục
            - th_action_mark_completed(): Đánh dấu yêu cầu đã hoàn thành

        3.2. approval_category:
            - create_new_request(): Tạo yêu cầu mới từ danh mục
            - write(): Cập nhật thông tin danh mục và người phê duyệt

        3.3. th_approval_sector:
            - th_action_view_category_to_request(): Hiển thị danh mục phê duyệt theo lĩnh vực

B. Cấu hình
    1. Lĩnh vực phê duyệt
        Mục đích: Phân chia các lĩnh vực phê duyệt khác nhau trong công ty.
        Ví dụ: Nhân sự, Tài chính, IT,...
        - Tại menu cấu hình, người dùng có thể tạo các lĩnh vực phê duyệt:
            • Tên lĩnh vực
            • Mô tả

    2. Loại phê duyệt
        Mục đích: Xác định các loại yêu cầu phê duyệt cụ thể.
        Ví dụ: Nghỉ phép, tuyển dụng nhân sự,...
        - Thông tin cấu hình:
            • Tên loại phê duyệt
            • Lĩnh vực phê duyệt
            • Quy trình phê duyệt
            • Những người phê duyệt
    3. Sản phẩm và biến thể sản phẩm
        Mục đích: Quản lý các sản phẩm và biến thể liên quan đến yêu cầu phê duyệt.
        Ví dụ: Sản phẩm A, Biến thể A1, A2,...
        - Thông tin cấu hình:
            • Tên sản phẩm
            • Thông tin chung
            • Thuộc tính và biến thể
            • Bán hàng
            • Mua hàng
            • Tồn kho
            • Kế toán
