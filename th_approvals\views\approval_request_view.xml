<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="th_base_group_user_all_request_approval_action" model="ir.actions.act_window">
            <field name="name">All approval</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">approval.request</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="domain">[('approver_ids.user_id.id','=', uid), ('request_status', 'in', ['pending','approved','refused','completed'])]</field>
        </record>

        <record id="approval_request_view_form_inherit" model="ir.ui.view">
            <field name="name">approval_request_view_form_inherit</field>
            <field name="model">approval.request</field>
            <field name="inherit_id" ref="approvals.approval_request_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form//header//field[@name='request_status']" position="before">
                    <field name="th_template_name" invisible="1"/>
                    <button name="action_download_template" type="object" string="Download Template"
                            attrs="{'invisible': ['|','|',('th_template_name', '=', False), ('request_status', '=', 'approved'), ('request_status', '=', 'refused')]}"/>
                </xpath>
                <xpath expr="//form//header//field[@name='request_status']" position="before">
                    <button name="th_action_mark_completed"
                            type="object"
                            string="Hoàn thành"
                            class="btn-primary"
                            attrs="{'invisible': [('request_status', '!=', 'approved')]}"/>
                </xpath>


                <xpath expr="//form//header//button[@name='action_approve']" position="attributes">
                    <attribute name="class">btn-primary</attribute>
                </xpath>
                <xpath expr="//page[@name='approvers']//field[@name='approver_ids']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='request_owner_id']" position="after">
                    <field name="th_sector_id" widget="many2one" options="{'no_create': True, 'no_open': True, 'no_edit': True}"/>
                </xpath>

                <xpath expr="//field[@name='category_id']" position="attributes">
                    <attribute name="domain">[('th_sector_id', '=', th_sector_id)]</attribute>
                    <attribute name="attrs">
                        {'invisible': [('th_sector_id', '=', False)]}
                    </attribute>
                </xpath>
            </field>
        </record>

        <menuitem id="th_base_group_user_all_request_approval_menuitem"
                  name="All approval" parent="approvals.approvals_approval_menu"
                  action="th_base_group_user_all_request_approval_action" sequence="30"/>

    </data>
</odoo>