from odoo import api, fields, models

class ThApprovalSector(models.Model):
    _name = 'th.approval.sector'
    _description = '<PERSON><PERSON><PERSON> vực phê duyệt'
    _order = 'sequence, id'

    name = fields.Char(string='Tên', required=True)
    th_description = fields.Text(string="Mô tả", required=True)
    sequence = fields.Integer(string="Sequence")

    def th_action_view_category_to_request(self):
        action = self.env["ir.actions.actions"]._for_xml_id("th_approvals.approval_category_of_section_action_new_request")
        context = {
            'default_th_sector_id': self.id,
        }
        action['context'] = context
        return action